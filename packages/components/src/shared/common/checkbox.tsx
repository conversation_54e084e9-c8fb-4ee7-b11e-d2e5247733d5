import clsx from 'clsx'
import type { FC } from 'react'
import { forwardRef, useId } from 'react'

interface CheckboxStylesProps {
  variant?: 'default' | 'bordered'
  size?: 'sm' | 'md' | 'lg'
  className?: string | undefined
  error?: boolean
  disabled: boolean
}

const useCheckboxStyles = ({ className, variant = 'default', size = 'md', error, disabled }: CheckboxStylesProps) =>
  clsx(
    'relative inline-flex items-center justify-center',
    'cursor-pointer rounded-b1', // styling
    'outline-primary',
    'transition-all duration-200', // animation
    'focus-visible:outline-2 focus-visible:outline-offset-2',
    {
      'cursor-not-allowed opacity-50': disabled,
      'w-4 h-4': size === 'sm',
      'w-5 h-5': size === 'md',
      'w-6 h-6': size === 'lg',
      'bg-white border border-accessory-1 hover:border-primary': variant === 'default' && !error,
      'bg-white border-2 border-accessory-1 hover:border-primary': variant === 'bordered' && !error,
      'border-error': error,
      'bg-primary border-primary': false, // Will be handled by checked state
    },
    className,
  )

const useCheckboxIconStyles = ({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) =>
  clsx('absolute inset-0 flex items-center justify-center', 'text-white transition-opacity duration-200', {
    'text-xs': size === 'sm',
    'text-sm': size === 'md',
    'text-base': size === 'lg',
  })

interface CheckboxProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'disabled'>,
    CheckboxStylesProps {
  label?: string
  description?: string
  indeterminate?: boolean
}

const Checkbox: FC<CheckboxProps> = forwardRef<HTMLInputElement, CheckboxProps>(
  (
    {
      className,
      variant = 'default',
      size = 'md',
      error = false,
      label,
      description,
      indeterminate = false,
      checked,
      disabled,
      ...props
    },
    ref,
  ) => {
    const checkboxStyles = useCheckboxStyles({ variant, size, className, error, disabled })
    const iconStyles = useCheckboxIconStyles({ size })
    const id = useId()

    const isChecked = indeterminate || checked

    return (
      <div className='flex items-start gap-3'>
        <div className='relative flex items-center'>
          <input
            ref={ref}
            id={id}
            type='checkbox'
            className='sr-only'
            checked={checked}
            disabled={disabled}
            {...props}
          />
          <div
            className={clsx(checkboxStyles, {
              'border-primary bg-primary': isChecked && !disabled,
              'border-dim-3 bg-dim-3': isChecked && disabled,
            })}>
            {isChecked && (
              <div className={iconStyles}>
                {indeterminate ? (
                  <svg width='12' height='2' viewBox='0 0 12 2' fill='none' aria-hidden='true'>
                    <path d='M1 1H11' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
                  </svg>
                ) : (
                  <svg width='12' height='9' viewBox='0 0 12 9' fill='none' aria-hidden='true'>
                    <path
                      d='M1 4.5L4.5 8L11 1'
                      stroke='currentColor'
                      strokeWidth='2'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    />
                  </svg>
                )}
              </div>
            )}
          </div>
        </div>

        {(label || description) && (
          <div className='flex flex-col gap-1'>
            {label && (
              <label
                htmlFor={id}
                className={clsx('cursor-pointer font-medium text-xs', {
                  'text-black': !disabled,
                  'text-dim-3': disabled,
                  'text-error': error,
                })}>
                {label}
              </label>
            )}
            {description && (
              <p
                className={clsx('text-2xs', {
                  'text-dim-2': !disabled,
                  'text-dim-3': disabled,
                })}>
                {description}
              </p>
            )}
          </div>
        )}
      </div>
    )
  },
)

Checkbox.displayName = 'Checkbox'

export { Checkbox }

import clsx from 'clsx'
import type { FC } from 'react'

export interface TitleStylesProps {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'primary'
  className?: string | undefined
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useTitleStyles = ({ variant, className }: TitleStylesProps) =>
  clsx(
    {
      'font-semibold text-md text-title-1': variant === 'h1',
      'font-semibold text-sm text-title-2': variant === 'h4',
      'font-medium text-xs text-title-2': variant === 'h5',
      'font-medium text-base text-primary': variant === 'primary',
    },
    className,
  )

export const Title: FC<
  TitleStylesProps & {
    el?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
    children: React.ReactNode
  }
> = ({ el = 'h1', variant = el, className, children, ...props }) => {
  const El = el
  const styles = useTitleStyles({ variant, className })

  return (
    <El className={styles} {...props}>
      {children}
    </El>
  )
}

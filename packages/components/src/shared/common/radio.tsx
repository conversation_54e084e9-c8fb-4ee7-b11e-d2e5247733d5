import clsx from 'clsx'
import React, { type FC, forwardRef, useId } from 'react'

interface RadioStylesProps {
  variant?: 'default' | 'bordered'
  size?: 'sm' | 'md' | 'lg'
  className?: string | undefined
  error?: boolean | undefined
  disabled?: boolean | undefined
}

const useRadioStyles = ({ className, variant = 'default', size = 'md', error, disabled }: RadioStylesProps) =>
  clsx(
    'relative inline-flex items-center justify-center',
    'cursor-pointer rounded-full', // styling - rounded-full for radio
    'outline-primary',
    'transition-all duration-200', // animation
    'focus-visible:outline-2 focus-visible:outline-offset-2',
    {
      'cursor-not-allowed opacity-50': disabled,
      'w-4 h-4': size === 'sm',
      'w-5 h-5': size === 'md',
      'w-6 h-6': size === 'lg',
      'bg-white border border-accessory-1 hover:border-primary': variant === 'default' && !error,
      'bg-white border-2 border-accessory-1 hover:border-primary': variant === 'bordered' && !error,
      'border-error': error,
    },
    className,
  )

const useRadioIconStyles = ({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) =>
  clsx('rounded-full transition-all duration-200', {
    'w-2 h-2': size === 'sm',
    'w-2.5 h-2.5': size === 'md',
    'w-3 h-3': size === 'lg',
  })

interface RadioProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'disabled'>, RadioStylesProps {
  label?: string
  description?: string
}

// Radio Group Component
interface RadioGroupProps {
  name: string
  value?: string
  onChange?: (value: string) => void
  children: React.ReactNode
  className?: string
  disabled?: boolean
  error?: boolean
}

export const Radio: FC<RadioProps> = forwardRef<HTMLInputElement, RadioProps>(
  (
    { className, variant = 'default', size = 'md', error = false, label, description, checked, disabled, ...props },
    ref,
  ) => {
    const radioStyles = useRadioStyles({ variant, size, className, error, disabled })
    const iconStyles = useRadioIconStyles({ size })
    const id = useId()

    return (
      <div className='flex items-start gap-3'>
        <div className='relative flex items-center'>
          <input ref={ref} id={id} type='radio' className='sr-only' checked={checked} disabled={disabled} {...props} />
          <div
            className={clsx(radioStyles, {
              'border-primary': checked && !disabled,
              'border-dim-3': checked && disabled,
            })}>
            {checked && (
              <div
                className={clsx(iconStyles, {
                  'bg-primary': !disabled,
                  'bg-dim-3': disabled,
                })}
              />
            )}
          </div>
        </div>

        {(label || description) && (
          <div className='flex flex-col gap-1'>
            {label && (
              <label
                htmlFor={id}
                className={clsx('cursor-pointer font-medium text-xs', {
                  'text-black': !disabled,
                  'text-dim-3': disabled,
                  'text-error': error,
                })}>
                {label}
              </label>
            )}
            {description && (
              <p
                className={clsx('text-2xs', {
                  'text-dim-2': !disabled,
                  'text-dim-3': disabled,
                })}>
                {description}
              </p>
            )}
          </div>
        )}
      </div>
    )
  },
)

export const RadioGroup: FC<RadioGroupProps> = ({
  name,
  value,
  onChange,
  children,
  className,
  disabled = false,
  error = false,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!disabled && onChange) {
      onChange(event.target.value)
    }
  }

  return (
    <div className={clsx('flex flex-col gap-3', className)} role='radiogroup'>
      {React.Children.map(children, child => {
        if (React.isValidElement<RadioProps>(child) && child.type === Radio) {
          return React.cloneElement(child, {
            name,
            checked: child.props.value === value,
            onChange: handleChange,
            disabled: (disabled || child.props.disabled) ?? false,
            error: error || child.props.error,
          })
        }
        return child
      })}
    </div>
  )
}

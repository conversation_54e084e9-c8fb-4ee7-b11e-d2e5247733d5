import clsx from 'clsx'
import React, { type FC, forwardRef, useId } from 'react'
import { Text } from './text'

interface RadioStylesProps {
  variant?: 'bordered'
  className?: string | undefined
  error?: boolean | undefined
  disabled?: boolean | undefined
}

const useRadioStyles = ({ className, variant = 'bordered', error, disabled }: RadioStylesProps) =>
  clsx(
    'relative inline-flex items-center justify-center',
    'cursor-pointer rounded-[6px] h-10 w-10', // styling - rounded-full for radio
    'outline-primary border',
    'transition-all duration-200', // animation
    'focus-visible:outline-2 focus-visible:outline-offset-2',
    {
      'cursor-not-allowed opacity-50': disabled,
      'bg-white border-accessory-1 hover:border-primary': variant === 'bordered' && !error,
      'border-error': error,
    },
    className,
  )

interface RadioProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'disabled'>, RadioStylesProps {
  label?: string
}

// Radio Group Component
interface RadioGroupProps {
  name: string
  value?: string
  onChange?: (value: string) => void
  children: React.ReactNode
  className?: string
  disabled?: boolean
  error?: boolean
}

export const Radio: FC<RadioProps> = forwardRef<HTMLInputElement, RadioProps>(
  ({ className, variant = 'bordered', error = false, label, checked, disabled, ...props }, ref) => {
    const radioStyles = useRadioStyles({ variant, className, error, disabled })
    const id = useId()

    return (
      <div className='flex items-center gap-3'>
        <div className='relative flex items-center'>
          <input ref={ref} id={id} type='radio' className='sr-only' checked={checked} disabled={disabled} {...props} />
          <label
            htmlFor={id}
            className={clsx(radioStyles, {
              'border-primary': checked && !disabled,
              'border-dim-3': checked && disabled,
            })}>
            {checked && (
              <div
                className={clsx(
                  'h-6 w-6 rounded-[4px]', // sizing
                  'transition-all duration-200', // animation
                  {
                    'bg-primary': !disabled,
                    'bg-dim-3': disabled,
                  },
                )}
              />
            )}
          </label>
        </div>

        {label && (
          <Text
            el='label'
            variant='dim-2'
            // @ts-expect-error
            htmlFor={id}
            className={clsx('cursor-pointer font-medium text-xs', {
              'text-dim-3': disabled,
              'text-error': error,
            })}>
            {label}
          </Text>
        )}
      </div>
    )
  },
)

export const RadioGroup: FC<RadioGroupProps> = ({
  name,
  value,
  onChange,
  children,
  className,
  disabled = false,
  error = false,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!disabled && onChange) {
      onChange(event.target.value)
    }
  }

  return (
    <div className={clsx('flex flex-row gap-x-12 gap-y-6', className)} role='radiogroup'>
      {React.Children.map(children, child => {
        if (React.isValidElement<RadioProps>(child) && child.type === Radio) {
          return React.cloneElement(child, {
            name,
            checked: child.props.value === value,
            onChange: handleChange,
            disabled: (disabled || child.props.disabled) ?? false,
            error,
          })
        }
        return child
      })}
    </div>
  )
}

import { subscriptionsApi } from '@mass/api'
import { subscription$ } from '@mass/components/dashboard'
import { createFileRoute } from '@tanstack/react-router'

function SubscriptionNotifications() {
  return <div className='grid gap-8 p-8 md:grid-cols-2 md:p-16 xl:grid-cols-3'>Notifications</div>
}

export const Route = createFileRoute('/_common/_subscriptions/$subscriptionId/notifications')({
  async beforeLoad({ params }) {
    const subscriptionId = params.subscriptionId

    const subscription = await subscriptionsApi.detail({
      query: {
        id: subscriptionId,
      },
    })

    subscription$.selectedSubscription.set(subscription)
  },

  component: SubscriptionNotifications,
})

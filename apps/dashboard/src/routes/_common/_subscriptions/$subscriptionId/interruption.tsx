import { subscriptionsApi } from '@mass/api'
import { subscription$ } from '@mass/components/dashboard'
import { createFileRoute } from '@tanstack/react-router'

function SubscriptionInterruption() {
  return <div className='grid gap-8 p-8 md:grid-cols-2 md:p-16 xl:grid-cols-3'>Interruption</div>
}

export const Route = createFileRoute('/_common/_subscriptions/$subscriptionId/interruption')({
  async beforeLoad({ params }) {
    const subscriptionId = params.subscriptionId

    const subscription = await subscriptionsApi.detail({
      query: {
        id: subscriptionId,
      },
    })

    subscription$.selectedSubscription.set(subscription)
  },

  component: SubscriptionInterruption,
})

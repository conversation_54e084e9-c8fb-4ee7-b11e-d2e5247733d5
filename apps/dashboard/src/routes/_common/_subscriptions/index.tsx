import { ObservableHint } from '@legendapp/state'
import { use$ } from '@legendapp/state/react'
import { global$, subscriptionsApi } from '@mass/api'
import { updateSubscription$ } from '@mass/components/dashboard'
import { Badge, Button, Link, Popover, Title, ui$ } from '@mass/components/shared'
import { BuildingIcon, DeleteIcon, DotsVerticalIcon, EditIcon } from '@mass/icons'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'

function Home() {
  const { t: common } = useTranslation('common')
  const { t: dashboard } = useTranslation('dashboard')
  const subscriptions = use$(() => global$.subscription.subscriptions.get()?.content ?? [])

  const getRegionName = (regionId: string) => {
    const targetRegion = (global$.subscription.regions.get() ?? []).find(region => region.id === regionId)
    return targetRegion?.name ?? ''
  }

  const handleEdit = (subscriptionId: string) => {
    // biome-ignore lint/style/noNonNullAssertion: Redundant
    const targetSubscription = subscriptions.find(subscription => subscription.id === subscriptionId)!

    updateSubscription$.original.set(targetSubscription)
    updateSubscription$.payload.set({
      name: targetSubscription.name,
    })

    ui$.onChangeModal('dashboard.update-subscription', true)
  }

  const handleDelete = (subscriptionId: string) => {
    ui$.confirmation.set({
      title: dashboard('subscriptions.delete'),
      description: dashboard('subscriptions.delete-description'),
      onConfirm: ObservableHint.function(async () => {
        await subscriptionsApi.delete({
          query: {
            id: subscriptionId,
          },
        })

        global$.subscription.subscriptions.set(
          await subscriptionsApi.list({
            invalidateCache: true,
          }),
        )
      }) as never,
      // biome-ignore lint/suspicious/noEmptyBlockStatements: Redundant
      onCancel: ObservableHint.function(() => {}) as never,
    })
    ui$.onChangeModal('confirmation', true)
  }

  return (
    <div
      className={clsx(
        'grid gap-8 md:grid-cols-2 xl:grid-cols-3', // grid
        'p-8 md:p-16', // spacing
      )}>
      {subscriptions.map(subscription => (
        <div className={clsx('relative h-full')} key={subscription.id}>
          <Link
            to='/$subscriptionId'
            params={{
              subscriptionId: subscription.id,
            }}
            className={clsx(
              'flex flex-col items-start gap-0!', // flex
              'h-full w-full', // sizing
              'rounded-b2 border border-accessory-1', // border
              'hover:border-dim-3',
            )}>
            <div
              className={clsx(
                'w-full', // sizing
                'flex flex-row items-center', // flex
                'gap-8 p-10', // spacing
              )}>
              <div
                className={clsx(
                  'flex flex-col items-center justify-center', // flex
                  'h-24 w-24', // sizing
                  'rounded-b2 border border-accessory-1', // border
                  'shadow-layer-2',
                )}>
                <BuildingIcon className='h-12 w-12 text-dim-1' />
              </div>

              <Title variant='h5' el='h2'>
                {subscription.name}
              </Title>
            </div>

            <div
              className={clsx(
                'w-full', // sizing
                'flex flex-row flex-wrap items-center', // flex
                'gap-8 px-10 pb-10', // spacing
              )}>
              <Badge withDot> {getRegionName(subscription.regionId)} </Badge>

              {subscription.type === 'electricity-production' && <Badge mode='success'>{common('production')}</Badge>}
            </div>
          </Link>

          <Popover
            className='absolute! top-0 right-0'
            variant='icon'
            popoverPosition='bottom end'
            buttonClassName={clsx('absolute right-10 top-10 w-auto!')}
            buttonContent={() => <DotsVerticalIcon className='text-dim-3' />}>
            <Button variant='hover-slim' className='justify-between!' onClick={() => handleEdit(subscription.id)}>
              {common('edit')}

              <EditIcon className='h-8 w-8' />
            </Button>

            <Button
              variant='error-slim'
              className='justify-between! mt-2'
              onClick={() => handleDelete(subscription.id)}>
              {common('delete')}

              <DeleteIcon className='h-8 w-8' />
            </Button>
          </Popover>
        </div>
      ))}
    </div>
  )
}

export const Route = createFileRoute('/_common/_subscriptions/')({
  component: Home,
})
